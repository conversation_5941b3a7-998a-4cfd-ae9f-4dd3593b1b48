#!/usr/bin/env python3
"""
傲基智能客服平台 - 后端启动脚本
解决模块导入问题
"""

import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
backend_path = project_root / "client" / "backend"
sys.path.insert(0, str(backend_path))

print("🚀 启动傲基智能客服平台后端...")
print(f"📁 项目根目录: {project_root}")
print(f"📁 后端目录: {backend_path}")
print(f"🐍 Python路径: {sys.path[:3]}")

try:
    # 尝试导入并启动应用
    import uvicorn
    
    # 切换到后端目录
    os.chdir(backend_path)
    print(f"📂 切换到目录: {os.getcwd()}")
    
    # 启动应用
    print("🔥 启动FastAPI应用...")
    uvicorn.run(
        "c_app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("💡 请确保已安装所有依赖:")
    print("   pip install -r client/backend/requirements.txt")
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print("💡 请检查配置和依赖")
