#!/usr/bin/env python3
"""
傲基智能客服平台 - 开发环境启动脚本
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    傲基智能客服平台                            ║
    ║                 Aukey AI Customer Service                    ║
    ║                                                              ║
    ║  基于 AutoGen 0.5.7 + FastAPI + React 的智能客服系统          ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查环境依赖"""
    print("🔍 检查环境依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 10):
        print("❌ Python版本需要3.10+")
        return False
    
    # 检查conda环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'aukey_autogen':
        print(f"⚠️  当前conda环境: {conda_env}, 建议使用: aukey_autogen")
    
    # 检查必要的目录
    required_dirs = [
        'client/backend',
        'frontend',
        'client/backend/data',
        'client/backend/logs'
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            print(f"📁 创建目录: {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
    
    print("✅ 环境检查完成")
    return True

def install_backend_deps():
    """安装后端依赖"""
    print("📦 检查后端依赖...")
    
    backend_dir = Path("client/backend")
    requirements_file = backend_dir / "requirements.txt"
    
    if not requirements_file.exists():
        print("⚠️  未找到requirements.txt，创建基础依赖文件...")
        create_requirements_file(requirements_file)
    
    try:
        # 检查是否需要安装依赖
        result = subprocess.run([
            sys.executable, "-c", 
            "import fastapi, uvicorn, autogen_agentchat, autogen_core, autogen_ext"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print("📦 安装后端依赖...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True)
        else:
            print("✅ 后端依赖已安装")
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装后端依赖失败: {e}")
        return False
    
    return True

def create_requirements_file(file_path):
    """创建基础的requirements.txt文件"""
    requirements = """
# 傲基智能客服平台 - Python依赖包
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AutoGen框架 (0.5.7版本)
autogen-agentchat==0.5.7
autogen-core==0.5.7
autogen-ext==0.5.7

# LLM客户端
openai==1.45.0
httpx==0.25.2

# 向量数据库
pymilvus==2.3.4
chromadb==0.4.18

# 文件处理
pypdf==3.17.1
docx2txt==0.8
python-multipart==0.0.6

# 数据处理
numpy==1.24.3
pandas==2.0.3

# 网络请求
requests==2.31.0
aiohttp==3.9.1

# 日志和监控
loguru==0.7.2

# 环境变量
python-dotenv==1.0.0

# 其他工具
typing-extensions==4.8.0
""".strip()
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(requirements)

def install_frontend_deps():
    """安装前端依赖"""
    print("📦 检查前端依赖...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ 前端目录不存在")
        return False
    
    package_json = frontend_dir / "package.json"
    if not package_json.exists():
        print("❌ package.json不存在")
        return False
    
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("📦 安装前端依赖...")
        try:
            subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装前端依赖失败: {e}")
            return False
    else:
        print("✅ 前端依赖已安装")
    
    return True

def create_env_file():
    """创建环境变量文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 创建环境变量文件...")
        env_content = """
# 傲基智能客服平台 - 环境变量配置
ENVIRONMENT=development
DEBUG=true

# LLM配置 (请填入您的API密钥)
OPENAI_API_KEY=your_api_key_here
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat

# 向量数据库配置
USE_MILVUS=false
CHROMA_DB_HOST=localhost
CHROMA_DB_PORT=8000

# 其他配置
LOG_LEVEL=INFO
""".strip()
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("⚠️  请编辑 .env 文件，填入您的API密钥")

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    backend_dir = Path("client/backend")
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "c_app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], cwd=backend_dir)
        
        return process
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    
    frontend_dir = Path("frontend")
    
    try:
        # 启动React开发服务器
        process = subprocess.Popen([
            "npm", "run", "dev"
        ], cwd=frontend_dir)
        
        return process
    except Exception as e:
        print(f"❌ 启动前端服务失败: {e}")
        return None

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 创建环境变量文件
    create_env_file()
    
    # 安装依赖
    if not install_backend_deps():
        sys.exit(1)
    
    if not install_frontend_deps():
        print("⚠️  前端依赖安装失败，仅启动后端服务")
        frontend_available = False
    else:
        frontend_available = True
    
    # 启动服务
    backend_process = start_backend()
    if not backend_process:
        sys.exit(1)
    
    frontend_process = None
    if frontend_available:
        time.sleep(3)  # 等待后端启动
        frontend_process = start_frontend()
    
    print("\n" + "="*60)
    print("🎉 服务启动成功！")
    print("📍 后端API: http://localhost:8000")
    print("📍 API文档: http://localhost:8000/docs")
    if frontend_available:
        print("📍 前端界面: http://localhost:3000")
    print("📍 按 Ctrl+C 停止服务")
    print("="*60 + "\n")
    
    # 等待中断信号
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        
        if backend_process:
            backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
