#!/usr/bin/env python3
"""
修复后端启动问题的脚本
"""

import os
import shutil
from pathlib import Path

def fix_main_file():
    """修复main.py文件"""
    print("🔧 修复后端main.py文件...")
    
    # 定义文件路径
    old_main = Path("client/backend/c_app/main.py")
    new_main = Path("client/backend/c_app/main_new.py")
    backup_main = Path("client/backend/c_app/main_backup.py")
    
    try:
        # 如果旧文件存在，先备份
        if old_main.exists():
            print(f"📁 备份原文件: {old_main} -> {backup_main}")
            shutil.copy2(old_main, backup_main)
        
        # 如果新文件存在，用它替换旧文件
        if new_main.exists():
            print(f"🔄 替换文件: {new_main} -> {old_main}")
            shutil.copy2(new_main, old_main)
            print("✅ main.py文件已更新")
        else:
            # 如果新文件不存在，创建一个
            print("📝 创建新的main.py文件...")
            create_new_main_file(old_main)
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    
    return True

def create_new_main_file(file_path):
    """创建新的main.py文件"""
    content = '''"""
傲基智能客服平台 - 主应用入口
基于FastAPI + AutoGen 0.5.7的智能客服系统
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os

from c_app.api import api_router
from c_app.core.config import settings

# 创建FastAPI应用实例
app = FastAPI(
    title="傲基智能客服平台API",
    description="基于RAG技术的专业电商智能客服系统API，使用AutoGen 0.5.7框架",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 静态文件服务（用于前端构建文件）
static_dir = os.path.join(os.path.dirname(__file__), "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "傲基智能客服平台API服务正常运行",
        "version": "1.0.0",
        "status": "healthy",
        "framework": "AutoGen 0.5.7",
        "llm_model": settings.LLM_MODEL
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "aukey-ai-customer-service",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "vector_db": "Milvus" if settings.USE_MILVUS else "ChromaDB"
    }

if __name__ == "__main__":
    print("🚀 启动傲基智能客服平台...")
    print(f"📡 API文档: http://localhost:8000/docs")
    print(f"🔧 配置环境: {settings.ENVIRONMENT}")
    print(f"🤖 LLM模型: {settings.LLM_MODEL}")
    print(f"🗄️ 向量数据库: {'Milvus' if settings.USE_MILVUS else 'ChromaDB'}")
    
    uvicorn.run(
        "c_app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
'''
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 创建新文件: {file_path}")

def check_directory_structure():
    """检查目录结构"""
    print("📁 检查目录结构...")
    
    required_dirs = [
        "client/backend/c_app",
        "client/backend/c_app/api",
        "client/backend/c_app/core",
        "client/backend/c_app/services",
        "client/backend/c_app/schemas",
        "client/backend/data",
        "client/backend/data/knowledge_base",
        "client/backend/data/sessions",
        "client/backend/data/memories",
        "client/backend/logs"
    ]
    
    for dir_path in required_dirs:
        path = Path(dir_path)
        if not path.exists():
            print(f"📁 创建目录: {dir_path}")
            path.mkdir(parents=True, exist_ok=True)
        else:
            print(f"✅ 目录存在: {dir_path}")

def check_required_files():
    """检查必要文件"""
    print("📄 检查必要文件...")
    
    required_files = [
        "client/backend/c_app/__init__.py",
        "client/backend/c_app/api/__init__.py",
        "client/backend/c_app/core/__init__.py",
        "client/backend/c_app/services/__init__.py",
        "client/backend/c_app/schemas/__init__.py"
    ]
    
    for file_path in required_files:
        path = Path(file_path)
        if not path.exists():
            print(f"📄 创建文件: {file_path}")
            path.touch()
            if file_path.endswith("__init__.py"):
                with open(path, 'w', encoding='utf-8') as f:
                    f.write("# Package initialization file\n")
        else:
            print(f"✅ 文件存在: {file_path}")

def main():
    """主函数"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    傲基智能客服平台                            ║
    ║                    后端修复工具                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # 检查并创建目录结构
    check_directory_structure()
    
    # 检查并创建必要文件
    check_required_files()
    
    # 修复main.py文件
    if fix_main_file():
        print("\n✅ 后端修复完成！")
        print("\n📍 现在可以尝试启动后端:")
        print("   cd client/backend")
        print("   python -m uvicorn c_app.main:app --host 0.0.0.0 --port 8000 --reload")
    else:
        print("\n❌ 后端修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
