# 傲基智能客服平台 - 环境变量配置示例
# 复制此文件为 .env 并填入实际配置值

# ================================
# 基础配置
# ================================
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# ================================
# LLM配置 (DeepSeek API)
# ================================
OPENAI_API_KEY=your_deepseek_api_key_here
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=4000

# ================================
# 向量数据库配置
# ================================

# Milvus (生产环境)
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_DATABASE=aukey_knowledge

# ChromaDB (开发环境备用)
CHROMA_DB_HOST=localhost
CHROMA_DB_PORT=8000
CHROMA_DB_SSL=false

# ================================
# 知识库配置
# ================================
KNOWLEDGE_BASE_DIR=data/knowledge_base
UPLOAD_MAX_SIZE=10485760
SUPPORTED_FILE_TYPES=.pdf,.docx,.txt,.md

# ================================
# 会话配置
# ================================
SESSION_STORAGE_DIR=data/sessions
MAX_SESSION_HISTORY=100
SESSION_TIMEOUT_HOURS=24

# ================================
# 记忆配置
# ================================
MEMORY_STORAGE_DIR=data/memories
VECTOR_DIMENSION=1536
SIMILARITY_THRESHOLD=0.7
MAX_RETRIEVAL_RESULTS=5

# ================================
# 日志配置
# ================================
LOG_LEVEL=INFO
LOG_DIR=logs

# ================================
# 电商API配置
# ================================
ECOMMERCE_API_BASE=http://localhost:8001
ECOMMERCE_API_TIMEOUT=30

# ================================
# CORS配置
# ================================
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://localhost:3000
