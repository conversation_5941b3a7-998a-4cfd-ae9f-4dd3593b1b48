#!/usr/bin/env python3
"""
测试导入是否正常
"""

import sys
import os
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path("client/backend")))

print("🧪 测试导入...")

try:
    print("1. 测试导入 c_app.main...")
    from c_app.main import app
    print("✅ c_app.main 导入成功")
    
    print("2. 测试导入 c_app.api...")
    from c_app.api import api_router
    print("✅ c_app.api 导入成功")
    
    print("3. 测试导入 c_app.core.config...")
    from c_app.core.config import settings
    print("✅ c_app.core.config 导入成功")
    
    print("4. 测试导入 c_app.services...")
    from c_app.services.memory_service import MemoryServiceFactory
    print("✅ c_app.services.memory_service 导入成功")
    
    print("5. 测试导入 c_app.services.chat_service_v4...")
    from c_app.services.chat_service_v4 import ChatService
    print("✅ c_app.services.chat_service_v4 导入成功")
    
    print("\n🎉 所有导入测试通过！")
    print("📡 现在可以启动后端服务:")
    print("   cd client/backend")
    print("   python -m uvicorn c_app.main:app --host 0.0.0.0 --port 8000 --reload")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()
