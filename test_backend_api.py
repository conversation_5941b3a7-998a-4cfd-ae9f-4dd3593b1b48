#!/usr/bin/env python3
"""
测试后端API的脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_create_session():
    """测试创建会话"""
    print("\n🔍 测试创建会话...")
    try:
        data = {"user_id": "test-user"}
        response = requests.post(
            f"{BASE_URL}/api/v1/chat/session/create",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result.get("session_id")
        else:
            print(f"错误响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

def test_stream_chat(session_id=None):
    """测试流式聊天"""
    print("\n🔍 测试流式聊天...")
    try:
        data = {
            "messages": [
                {
                    "role": "user",
                    "content": "你好，我想咨询商品信息"
                }
            ],
            "user_id": "test-user"
        }
        
        if session_id:
            data["session_id"] = session_id
        
        response = requests.post(
            f"{BASE_URL}/api/v1/chat/stream",
            json=data,
            headers={"Content-Type": "application/json"},
            stream=True
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("流式响应内容:")
            full_content = ""
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 去掉 'data: ' 前缀
                        if data_str == '[DONE]':
                            print("\n✅ 流式响应完成")
                            break
                        try:
                            chunk_data = json.loads(data_str)
                            content = chunk_data.get('content', '')
                            if content:
                                print(content, end='', flush=True)
                                full_content += content
                        except json.JSONDecodeError:
                            continue
            
            print(f"\n\n完整响应: {full_content}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def test_get_sessions():
    """测试获取用户会话列表"""
    print("\n🔍 测试获取用户会话列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/chat/sessions/test-user")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"会话列表: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"错误响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试傲基智能客服后端API...")
    
    # 测试健康检查
    if not test_health():
        print("❌ 健康检查失败，请确保后端服务正在运行")
        return
    
    # 测试创建会话
    session_id = test_create_session()
    if not session_id:
        print("❌ 创建会话失败")
        return
    
    # 测试流式聊天
    if not test_stream_chat(session_id):
        print("❌ 流式聊天失败")
        return
    
    # 测试获取会话列表
    if not test_get_sessions():
        print("❌ 获取会话列表失败")
        return
    
    print("\n🎉 所有API测试通过！后端服务正常工作。")
    print("\n💡 如果前端还是404，可能是前端缓存问题，建议:")
    print("1. 清除浏览器缓存")
    print("2. 重启前端服务")
    print("3. 检查前端控制台的网络请求")

if __name__ == "__main__":
    main()
