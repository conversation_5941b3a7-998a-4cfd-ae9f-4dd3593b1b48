#!/usr/bin/env python3
"""
后端问题诊断脚本
"""

import sys
import os
from pathlib import Path

def diagnose_backend():
    print("🔍 诊断后端问题...")
    
    # 检查当前工作目录
    current_dir = Path.cwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 检查backend目录
    backend_dir = current_dir / "client" / "backend"
    print(f"📁 后端目录: {backend_dir}")
    print(f"📁 后端目录存在: {backend_dir.exists()}")
    
    if backend_dir.exists():
        print(f"📁 后端目录内容:")
        for item in backend_dir.iterdir():
            print(f"   {item.name} ({'目录' if item.is_dir() else '文件'})")
    
    # 检查c_app目录
    c_app_dir = backend_dir / "c_app"
    print(f"\n📁 c_app目录: {c_app_dir}")
    print(f"📁 c_app目录存在: {c_app_dir.exists()}")
    
    if c_app_dir.exists():
        print(f"📁 c_app目录内容:")
        for item in c_app_dir.iterdir():
            print(f"   {item.name} ({'目录' if item.is_dir() else '文件'})")
    
    # 检查main.py文件
    main_file = c_app_dir / "main.py"
    print(f"\n📄 main.py文件: {main_file}")
    print(f"📄 main.py存在: {main_file.exists()}")
    
    if main_file.exists():
        print(f"📄 main.py文件大小: {main_file.stat().st_size} bytes")
        
        # 尝试读取文件内容的前几行
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:10]
                print(f"📄 main.py前10行:")
                for i, line in enumerate(lines, 1):
                    print(f"   {i:2d}: {line.rstrip()}")
        except Exception as e:
            print(f"❌ 读取main.py失败: {e}")
    
    # 检查Python路径
    print(f"\n🐍 Python路径:")
    for i, path in enumerate(sys.path[:5]):
        print(f"   {i}: {path}")
    
    # 尝试导入测试
    print(f"\n🧪 导入测试:")
    
    # 切换到backend目录进行测试
    original_cwd = os.getcwd()
    try:
        os.chdir(backend_dir)
        print(f"📂 切换到: {os.getcwd()}")
        
        # 测试导入c_app
        try:
            import c_app
            print("✅ 成功导入 c_app")
        except Exception as e:
            print(f"❌ 导入 c_app 失败: {e}")
        
        # 测试导入c_app.main
        try:
            import c_app.main
            print("✅ 成功导入 c_app.main")
        except Exception as e:
            print(f"❌ 导入 c_app.main 失败: {e}")
        
        # 测试导入app
        try:
            from c_app.main import app
            print("✅ 成功导入 app")
        except Exception as e:
            print(f"❌ 导入 app 失败: {e}")
            
    finally:
        os.chdir(original_cwd)
    
    # 检查依赖
    print(f"\n📦 依赖检查:")
    required_packages = ["fastapi", "uvicorn", "pydantic"]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")

def suggest_solutions():
    print(f"\n💡 建议的解决方案:")
    print("1. 确保在正确的目录运行:")
    print("   cd client/backend")
    print("   python -m uvicorn c_app.main:app --host 0.0.0.0 --port 8000 --reload")
    
    print("\n2. 或者使用我们的启动脚本:")
    print("   python run_server.py")
    
    print("\n3. 检查依赖安装:")
    print("   pip install -r requirements.txt")
    
    print("\n4. 如果还有问题，尝试重新安装:")
    print("   pip uninstall fastapi uvicorn")
    print("   pip install fastapi uvicorn")

if __name__ == "__main__":
    diagnose_backend()
    suggest_solutions()
