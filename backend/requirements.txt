# 傲基智能客服平台 - Python依赖包
# 基于AutoGen 0.5.7的电商智能客服系统

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AutoGen框架 (0.5.7版本)
autogen-agentchat==0.5.7
autogen-core==0.5.7
autogen-ext==0.5.7

# LLM客户端
openai==1.45.0
httpx==0.25.2

# 向量数据库
# Mi<PERSON><PERSON>s (生产环境)
pymilvus==2.3.4
# ChromaDB (开发环境备用)
chromadb==0.4.18

# 文件处理
pypdf==3.17.1
docx2txt==0.8
python-multipart==0.0.6

# 数据处理
numpy==1.24.3
pandas==2.0.3

# 网络请求
requests==2.31.0
aiohttp==3.9.1

# 日志和监控
loguru==0.7.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0

# 环境变量
python-dotenv==1.0.0

# 其他工具
typing-extensions==4.8.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
