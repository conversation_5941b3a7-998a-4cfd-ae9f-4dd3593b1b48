"""
傲基智能客服平台 - 核心配置
优化版本：专注于Milvus向量数据库，支持AutoGen 0.5.7
"""

import os
import secrets
from typing import List, Optional
from pydantic_settings import BaseSettings


def get_default_cors_origins() -> List[str]:
    """获取默认的CORS允许源"""
    return [
        "http://localhost:3000",
        "http://localhost:8000", 
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8000",
        "https://localhost:3000",
        "https://127.0.0.1:3000",
        # WebSocket支持
        "ws://localhost:3000",
        "ws://127.0.0.1:3000",
        "wss://localhost:3000", 
        "wss://127.0.0.1:3000",
    ]


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # 访问令牌配置
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7天
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = get_default_cors_origins()
    
    # LLM配置 (支持DeepSeek等OpenAI兼容API)
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: Optional[str] = "https://api.deepseek.com/v1"
    LLM_MODEL: str = "deepseek-chat"
    LLM_TEMPERATURE: float = 0.7
    LLM_MAX_TOKENS: int = 4000
    
    # 向量数据库配置 - Milvus (主要)
    MILVUS_HOST: str = "localhost"
    MILVUS_PORT: int = 19530
    MILVUS_USER: Optional[str] = None
    MILVUS_PASSWORD: Optional[str] = None
    MILVUS_DATABASE: str = "aukey_knowledge"
    MILVUS_COLLECTION_PREFIX: str = "aukey_"
    USE_MILVUS: bool = True  # 优先使用Milvus
    
    # ChromaDB (开发环境备用，当Milvus不可用时)
    CHROMA_DB_HOST: str = "localhost"
    CHROMA_DB_PORT: int = 8000
    CHROMA_DB_SSL: bool = False
    
    # 知识库配置
    KNOWLEDGE_BASE_DIR: str = "data/knowledge_base"
    UPLOAD_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    SUPPORTED_FILE_TYPES: List[str] = [".pdf", ".docx", ".txt", ".md"]
    
    # 会话配置
    SESSION_STORAGE_DIR: str = "data/sessions"
    MAX_SESSION_HISTORY: int = 100
    SESSION_TIMEOUT_HOURS: int = 24
    
    # 记忆配置
    MEMORY_STORAGE_DIR: str = "data/memories"
    VECTOR_DIMENSION: int = 1536  # OpenAI embedding维度
    SIMILARITY_THRESHOLD: float = 0.7
    MAX_RETRIEVAL_RESULTS: int = 5
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "logs"
    
    # 电商API配置
    ECOMMERCE_API_BASE: str = "http://localhost:8001"
    ECOMMERCE_API_TIMEOUT: int = 30
    
    # AutoGen配置
    AUTOGEN_CACHE_SEED: int = 42
    AUTOGEN_MAX_CONSECUTIVE_AUTO_REPLY: int = 10
    
    # 系统提示词
    SYSTEM_PROMPT: str = """你是傲基智能客服助手，专门为电商平台提供专业的客户服务。

你的职责包括：
1. 回答客户关于商品的咨询
2. 处理订单查询和物流跟踪
3. 协助处理退换货申请
4. 解答平台政策和规则
5. 提供专业的购物建议

请始终保持：
- 友好、专业的服务态度
- 准确、及时的信息回复
- 耐心、细致的问题解答
- 主动、贴心的服务意识

如果遇到无法解决的问题，请及时转接人工客服。"""

    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
for directory in [
    settings.KNOWLEDGE_BASE_DIR,
    settings.SESSION_STORAGE_DIR, 
    settings.MEMORY_STORAGE_DIR,
    settings.LOG_DIR
]:
    os.makedirs(directory, exist_ok=True)
