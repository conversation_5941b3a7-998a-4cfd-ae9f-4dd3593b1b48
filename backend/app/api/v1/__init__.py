"""
傲基智能客服平台 - API v1版本路由
"""

from fastapi import APIRouter
from app.api.v1 import chat, knowledge, session

# 创建v1 API路由器
api_router = APIRouter()

# 包含各个功能模块的路由
api_router.include_router(chat.router, prefix="/chat", tags=["聊天"])
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["知识库"])
api_router.include_router(session.router, prefix="/session", tags=["会话管理"])

__all__ = ["api_router"]
