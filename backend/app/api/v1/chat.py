"""
傲基智能客服平台 - 聊天API路由
支持流式对话响应
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any
import json
import asyncio
import logging

from app.schemas.chat import ChatRequest, ChatMessage, ChatStreamResponse
from app.services.chat_service import ChatService
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

# 初始化聊天服务
chat_service = ChatService()


@router.post("/stream")
async def chat_stream(request: ChatRequest):
    """
    流式聊天API
    支持实时流式响应，类似ChatGPT的体验
    """
    try:
        logger.info(f"收到聊天请求: user_id={request.user_id}, session_id={request.session_id}")
        
        # 验证请求
        if not request.messages:
            raise HTTPException(status_code=400, detail="消息列表不能为空")
        
        # 获取最后一条用户消息
        last_message = request.messages[-1]
        if last_message.role != "user":
            raise HTTPException(status_code=400, detail="最后一条消息必须是用户消息")
        
        # 创建流式响应生成器
        async def generate_response():
            try:
                # 调用聊天服务进行流式响应
                async for chunk in chat_service.chat_stream(request):
                    # 格式化为SSE格式
                    data = {
                        "type": "content",
                        "content": chunk.content if hasattr(chunk, 'content') else str(chunk),
                        "role": "assistant"
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                
                # 发送结束信号
                yield f"data: {json.dumps({'type': 'done'}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                logger.error(f"流式响应生成错误: {e}")
                error_data = {
                    "type": "error",
                    "error": str(e)
                }
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )
        
    except Exception as e:
        logger.error(f"聊天API错误: {e}")
        raise HTTPException(status_code=500, detail=f"聊天服务错误: {str(e)}")


@router.post("/message")
async def send_message(request: ChatRequest):
    """
    发送单条消息（非流式）
    用于不需要流式响应的场景
    """
    try:
        logger.info(f"收到非流式聊天请求: user_id={request.user_id}")
        
        # 验证请求
        if not request.messages:
            raise HTTPException(status_code=400, detail="消息列表不能为空")
        
        # 调用聊天服务
        response = await chat_service.chat_single(request)
        
        return {
            "message": response.content,
            "role": response.role,
            "session_id": request.session_id,
            "timestamp": response.timestamp
        }
        
    except Exception as e:
        logger.error(f"单条消息API错误: {e}")
        raise HTTPException(status_code=500, detail=f"聊天服务错误: {str(e)}")


@router.get("/models")
async def get_available_models():
    """获取可用的LLM模型列表"""
    return {
        "models": [
            {
                "id": settings.LLM_MODEL,
                "name": "DeepSeek Chat",
                "description": "DeepSeek智能对话模型",
                "is_default": True
            }
        ],
        "default_model": settings.LLM_MODEL
    }
