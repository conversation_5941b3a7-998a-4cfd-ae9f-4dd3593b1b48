"""
傲基智能客服平台 - 聊天相关数据模型
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色: user, assistant, system")
    content: str = Field(..., description="消息内容")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="消息时间戳")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="消息元数据")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    user_id: str = Field(default="anonymous", description="用户ID")
    session_id: Optional[str] = Field(default=None, description="会话ID")
    model: Optional[str] = Field(default=None, description="指定使用的模型")
    temperature: Optional[float] = Field(default=None, description="生成温度")
    max_tokens: Optional[int] = Field(default=None, description="最大生成token数")
    stream: bool = Field(default=True, description="是否使用流式响应")
    use_knowledge_base: bool = Field(default=True, description="是否使用知识库")
    knowledge_types: Optional[List[str]] = Field(default=None, description="指定知识库类型")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    content: str = Field(..., description="回复内容")
    role: str = Field(default="assistant", description="回复角色")
    session_id: str = Field(..., description="会话ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="回复时间戳")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="回复元数据")


class StreamChunk(BaseModel):
    """流式响应块模型"""
    content: str = Field(..., description="内容块")
    is_final: bool = Field(default=False, description="是否为最后一块")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="块元数据")


class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    title: Optional[str] = Field(default=None, description="会话标题")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    message_count: int = Field(default=0, description="消息数量")
    is_active: bool = Field(default=True, description="是否活跃")


class SessionRequest(BaseModel):
    """会话请求模型"""
    user_id: str = Field(..., description="用户ID")
    title: Optional[str] = Field(default=None, description="会话标题")


class SessionResponse(BaseModel):
    """会话响应模型"""
    session_id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    title: Optional[str] = Field(default=None, description="会话标题")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    messages: List[ChatMessage] = Field(default=[], description="会话消息列表")
    message_count: int = Field(default=0, description="消息数量")
    is_active: bool = Field(default=True, description="是否活跃")


class KnowledgeQuery(BaseModel):
    """知识库查询模型"""
    query: str = Field(..., description="查询内容")
    knowledge_types: Optional[List[str]] = Field(default=None, description="知识库类型")
    limit: int = Field(default=5, description="返回结果数量限制")
    similarity_threshold: float = Field(default=0.7, description="相似度阈值")


class KnowledgeResult(BaseModel):
    """知识库查询结果模型"""
    content: str = Field(..., description="知识内容")
    source: str = Field(..., description="知识来源")
    similarity: float = Field(..., description="相似度分数")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="知识元数据")


class FileUploadRequest(BaseModel):
    """文件上传请求模型"""
    user_id: str = Field(..., description="用户ID")
    knowledge_type: str = Field(default="general", description="知识库类型")
    description: Optional[str] = Field(default=None, description="文件描述")


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    file_id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="文件名")
    size: int = Field(..., description="文件大小")
    knowledge_type: str = Field(..., description="知识库类型")
    status: str = Field(..., description="处理状态")
    message: str = Field(..., description="处理消息")
    uploaded_at: datetime = Field(default_factory=datetime.now, description="上传时间")
