"""
傲基智能客服平台 - 电商相关数据模型
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field


class Product(BaseModel):
    """商品模型"""
    product_id: str = Field(..., description="商品ID")
    name: str = Field(..., description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    price: Decimal = Field(..., description="商品价格")
    original_price: Optional[Decimal] = Field(default=None, description="原价")
    category: str = Field(..., description="商品分类")
    brand: Optional[str] = Field(default=None, description="品牌")
    stock: int = Field(default=0, description="库存数量")
    images: List[str] = Field(default=[], description="商品图片URL列表")
    attributes: Optional[Dict[str, Any]] = Field(default=None, description="商品属性")
    rating: Optional[float] = Field(default=None, description="商品评分")
    review_count: int = Field(default=0, description="评价数量")
    is_available: bool = Field(default=True, description="是否可购买")


class ProductSearchRequest(BaseModel):
    """商品搜索请求模型"""
    query: str = Field(..., description="搜索关键词")
    category: Optional[str] = Field(default=None, description="商品分类")
    brand: Optional[str] = Field(default=None, description="品牌")
    min_price: Optional[Decimal] = Field(default=None, description="最低价格")
    max_price: Optional[Decimal] = Field(default=None, description="最高价格")
    sort_by: str = Field(default="relevance", description="排序方式")
    limit: int = Field(default=10, description="返回结果数量")
    offset: int = Field(default=0, description="结果偏移量")


class Order(BaseModel):
    """订单模型"""
    order_id: str = Field(..., description="订单ID")
    user_id: str = Field(..., description="用户ID")
    order_number: str = Field(..., description="订单号")
    status: str = Field(..., description="订单状态")
    total_amount: Decimal = Field(..., description="订单总金额")
    currency: str = Field(default="USD", description="货币类型")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    shipping_address: Optional[Dict[str, str]] = Field(default=None, description="收货地址")
    payment_method: Optional[str] = Field(default=None, description="支付方式")
    items: List["OrderItem"] = Field(default=[], description="订单商品列表")
    tracking_info: Optional["TrackingInfo"] = Field(default=None, description="物流信息")


class OrderItem(BaseModel):
    """订单商品项模型"""
    item_id: str = Field(..., description="商品项ID")
    product_id: str = Field(..., description="商品ID")
    product_name: str = Field(..., description="商品名称")
    quantity: int = Field(..., description="购买数量")
    unit_price: Decimal = Field(..., description="单价")
    total_price: Decimal = Field(..., description="小计")
    attributes: Optional[Dict[str, str]] = Field(default=None, description="商品规格")


class TrackingInfo(BaseModel):
    """物流跟踪信息模型"""
    tracking_number: str = Field(..., description="物流单号")
    carrier: str = Field(..., description="承运商")
    status: str = Field(..., description="物流状态")
    estimated_delivery: Optional[datetime] = Field(default=None, description="预计送达时间")
    tracking_events: List["TrackingEvent"] = Field(default=[], description="物流事件列表")


class TrackingEvent(BaseModel):
    """物流事件模型"""
    timestamp: datetime = Field(..., description="事件时间")
    location: str = Field(..., description="事件地点")
    description: str = Field(..., description="事件描述")
    status: str = Field(..., description="事件状态")


class ReturnRequest(BaseModel):
    """退货申请模型"""
    return_id: str = Field(..., description="退货ID")
    order_id: str = Field(..., description="订单ID")
    user_id: str = Field(..., description="用户ID")
    product_id: str = Field(..., description="商品ID")
    reason: str = Field(..., description="退货原因")
    description: Optional[str] = Field(default=None, description="详细说明")
    status: str = Field(default="pending", description="申请状态")
    created_at: datetime = Field(default_factory=datetime.now, description="申请时间")
    processed_at: Optional[datetime] = Field(default=None, description="处理时间")
    refund_amount: Optional[Decimal] = Field(default=None, description="退款金额")


class ReturnRequestCreate(BaseModel):
    """创建退货申请模型"""
    order_id: str = Field(..., description="订单ID")
    product_id: str = Field(..., description="商品ID")
    reason: str = Field(..., description="退货原因")
    description: Optional[str] = Field(default=None, description="详细说明")


class Promotion(BaseModel):
    """促销活动模型"""
    promotion_id: str = Field(..., description="促销ID")
    title: str = Field(..., description="促销标题")
    description: str = Field(..., description="促销描述")
    type: str = Field(..., description="促销类型")
    discount_value: Decimal = Field(..., description="折扣值")
    discount_type: str = Field(..., description="折扣类型")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    applicable_products: Optional[List[str]] = Field(default=None, description="适用商品")
    min_order_amount: Optional[Decimal] = Field(default=None, description="最低订单金额")
    is_active: bool = Field(default=True, description="是否激活")


class Policy(BaseModel):
    """政策规则模型"""
    policy_id: str = Field(..., description="政策ID")
    title: str = Field(..., description="政策标题")
    content: str = Field(..., description="政策内容")
    category: str = Field(..., description="政策分类")
    version: str = Field(..., description="版本号")
    effective_date: datetime = Field(..., description="生效日期")
    is_active: bool = Field(default=True, description="是否有效")


class CustomerFeedback(BaseModel):
    """客户反馈模型"""
    feedback_id: str = Field(..., description="反馈ID")
    user_id: str = Field(..., description="用户ID")
    type: str = Field(..., description="反馈类型")
    subject: str = Field(..., description="反馈主题")
    content: str = Field(..., description="反馈内容")
    rating: Optional[int] = Field(default=None, description="评分")
    status: str = Field(default="pending", description="处理状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    response: Optional[str] = Field(default=None, description="回复内容")
    responded_at: Optional[datetime] = Field(default=None, description="回复时间")


class CustomerFeedbackCreate(BaseModel):
    """创建客户反馈模型"""
    type: str = Field(..., description="反馈类型")
    subject: str = Field(..., description="反馈主题")
    content: str = Field(..., description="反馈内容")
    rating: Optional[int] = Field(default=None, description="评分")
