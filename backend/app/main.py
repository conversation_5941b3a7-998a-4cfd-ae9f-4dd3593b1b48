"""
傲基智能客服平台 - 主应用入口
基于FastAPI + AutoGen 0.5.7的智能客服系统
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os

from app.api import api_router
from app.core.config import settings

# 创建FastAPI应用实例
app = FastAPI(
    title="傲基智能客服平台API",
    description="基于RAG技术的专业电商智能客服系统API，使用AutoGen 0.5.7框架",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 静态文件服务（用于前端构建文件）
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "傲基智能客服平台API服务正常运行",
        "version": "1.0.0",
        "status": "healthy",
        "framework": "AutoGen 0.5.7",
        "llm_model": settings.LLM_MODEL
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "aukey-ai-customer-service",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "vector_db": "Milvus" if settings.USE_MILVUS else "ChromaDB"
    }

if __name__ == "__main__":
    print("🚀 启动傲基智能客服平台...")
    print(f"📡 API文档: http://localhost:8000/docs")
    print(f"🔧 配置环境: {settings.ENVIRONMENT}")
    print(f"🤖 LLM模型: {settings.LLM_MODEL}")
    print(f"🗄️ 向量数据库: {'Milvus' if settings.USE_MILVUS else 'ChromaDB'}")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
