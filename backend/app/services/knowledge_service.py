"""
傲基智能客服平台 - 知识库服务
支持Milvus和ChromaDB的RAG知识检索
"""

import os
import logging
import uuid
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from pathlib import Path

from autogen_core.memory import MemoryContent, MemoryMimeType
from autogen_ext.memory.chromadb import ChromaDBVectorMemory, HttpChromaDBVectorMemoryConfig
from fastapi import UploadFile

from app.core.config import settings
from app.schemas.chat import KnowledgeQuery, KnowledgeResult, FileUploadResponse


class KnowledgeType:
    """知识库类型常量"""
    CUSTOMER_SERVICE = "customer_service"  # 客服知识库
    PRODUCT_INFO = "product_info"          # 商品信息
    POLICY_RULES = "policy_rules"          # 政策规则
    FAQ = "faq"                           # 常见问题
    TECHNICAL_DOCS = "technical_docs"      # 技术文档
    USER_MANUAL = "user_manual"           # 用户手册


class KnowledgeService:
    """知识库服务类"""
    
    def __init__(self, knowledge_type: str = KnowledgeType.CUSTOMER_SERVICE):
        self.knowledge_type = knowledge_type
        self.logger = logging.getLogger(f"KnowledgeService.{knowledge_type}")
        
        # 初始化向量数据库
        self.vector_memory = self._init_vector_memory()
        
        # 支持的文件类型
        self.supported_extensions = settings.SUPPORTED_FILE_TYPES
        
        self.logger.info(f"知识库服务初始化完成: {knowledge_type}")
    
    def _init_vector_memory(self) -> ChromaDBVectorMemory:
        """初始化向量内存"""
        collection_name = f"aukey_{self.knowledge_type}"
        
        config = HttpChromaDBVectorMemoryConfig(
            host=settings.CHROMA_DB_HOST,
            port=settings.CHROMA_DB_PORT,
            collection_name=collection_name,
            k=settings.MAX_RETRIEVAL_RESULTS,
            score_threshold=settings.SIMILARITY_THRESHOLD
        )
        
        return ChromaDBVectorMemory(config=config)
    
    async def add_text(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """添加文本内容到知识库"""
        try:
            # 准备元数据
            if metadata is None:
                metadata = {}
            
            metadata.update({
                "knowledge_type": self.knowledge_type,
                "content_type": "text",
                "added_at": datetime.now().isoformat(),
                "id": str(uuid.uuid4())
            })
            
            # 创建内存内容
            memory_content = MemoryContent(
                content=content,
                mime_type=MemoryMimeType.TEXT,
                metadata=metadata
            )
            
            # 添加到向量数据库
            await self.vector_memory.add(memory_content)
            
            self.logger.info(f"成功添加文本内容到知识库: {len(content)} 字符")
            return metadata["id"]
            
        except Exception as e:
            self.logger.error(f"添加文本内容失败: {e}")
            raise
    
    async def add_file(self, file: UploadFile, metadata: Optional[Dict[str, Any]] = None) -> FileUploadResponse:
        """添加文件到知识库"""
        try:
            # 验证文件类型
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in self.supported_extensions:
                raise ValueError(f"不支持的文件类型: {file_ext}")
            
            # 验证文件大小
            file_content = await file.read()
            if len(file_content) > settings.UPLOAD_MAX_SIZE:
                raise ValueError(f"文件大小超过限制: {len(file_content)} bytes")
            
            # 保存临时文件
            temp_file_path = f"temp_{uuid.uuid4()}{file_ext}"
            with open(temp_file_path, "wb") as temp_file:
                temp_file.write(file_content)
            
            try:
                # 提取文本内容
                text_content = self._extract_text_from_file(temp_file_path, file_ext)
                
                # 准备元数据
                if metadata is None:
                    metadata = {}
                
                file_id = str(uuid.uuid4())
                metadata.update({
                    "file_id": file_id,
                    "filename": file.filename,
                    "file_type": file_ext,
                    "file_size": len(file_content),
                    "knowledge_type": self.knowledge_type,
                    "content_type": "file",
                    "uploaded_at": datetime.now().isoformat()
                })
                
                # 添加到知识库
                await self.add_text(text_content, metadata)
                
                return FileUploadResponse(
                    file_id=file_id,
                    filename=file.filename,
                    size=len(file_content),
                    knowledge_type=self.knowledge_type,
                    status="success",
                    message="文件上传并处理成功"
                )
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    
        except Exception as e:
            self.logger.error(f"文件上传失败: {e}")
            raise
    
    def _extract_text_from_file(self, file_path: str, file_ext: str) -> str:
        """从文件中提取文本内容"""
        if file_ext == '.pdf':
            from pypdf import PdfReader
            text = ""
            with open(file_path, 'rb') as f:
                pdf = PdfReader(f)
                for page in pdf.pages:
                    text += page.extract_text() + "\n\n"
            return text.strip()
        
        elif file_ext == '.docx':
            import docx2txt
            return docx2txt.process(file_path)
        
        elif file_ext in ['.txt', '.md']:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        else:
            raise ValueError(f"不支持的文件类型: {file_ext}")
    
    async def search(self, query: str, limit: int = None, threshold: float = None) -> List[KnowledgeResult]:
        """搜索知识库"""
        try:
            if limit is None:
                limit = settings.MAX_RETRIEVAL_RESULTS
            if threshold is None:
                threshold = settings.SIMILARITY_THRESHOLD
            
            # 执行向量搜索
            results = await self.vector_memory.retrieve(
                MemoryContent(content=query, mime_type=MemoryMimeType.TEXT),
                k=limit
            )
            
            # 转换结果格式
            knowledge_results = []
            for result in results:
                metadata = result.metadata or {}
                
                knowledge_result = KnowledgeResult(
                    content=result.content,
                    source=metadata.get("filename", "unknown"),
                    similarity=metadata.get("score", 0.0),
                    metadata=metadata
                )
                
                # 过滤低相似度结果
                if knowledge_result.similarity >= threshold:
                    knowledge_results.append(knowledge_result)
            
            self.logger.info(f"知识库搜索完成: 查询='{query}', 结果数={len(knowledge_results)}")
            return knowledge_results
            
        except Exception as e:
            self.logger.error(f"知识库搜索失败: {e}")
            return []
    
    async def get_knowledge_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            # 这里可以添加获取统计信息的逻辑
            # 由于ChromaDB的限制，暂时返回基础信息
            return {
                "knowledge_type": self.knowledge_type,
                "collection_name": f"aukey_{self.knowledge_type}",
                "status": "active",
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"获取知识库统计失败: {e}")
            return {}


class KnowledgeServiceManager:
    """知识库服务管理器"""
    
    def __init__(self):
        self._services: Dict[str, KnowledgeService] = {}
        self.logger = logging.getLogger("KnowledgeServiceManager")
    
    def get_service(self, knowledge_type: str) -> KnowledgeService:
        """获取指定类型的知识库服务"""
        if knowledge_type not in self._services:
            self._services[knowledge_type] = KnowledgeService(knowledge_type)
        return self._services[knowledge_type]
    
    async def search_all(self, query: str, knowledge_types: Optional[List[str]] = None) -> List[KnowledgeResult]:
        """在多个知识库中搜索"""
        if knowledge_types is None:
            knowledge_types = [
                KnowledgeType.CUSTOMER_SERVICE,
                KnowledgeType.FAQ,
                KnowledgeType.PRODUCT_INFO,
                KnowledgeType.POLICY_RULES
            ]
        
        all_results = []
        for knowledge_type in knowledge_types:
            service = self.get_service(knowledge_type)
            results = await service.search(query)
            all_results.extend(results)
        
        # 按相似度排序
        all_results.sort(key=lambda x: x.similarity, reverse=True)
        
        # 限制返回结果数量
        return all_results[:settings.MAX_RETRIEVAL_RESULTS]


# 创建全局知识库服务管理器
knowledge_manager = KnowledgeServiceManager()
