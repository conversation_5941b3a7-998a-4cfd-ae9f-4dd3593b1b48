"""
傲基智能客服平台 - 电商工具集
为智能体提供电商相关的工具函数
"""

import json
import logging
from typing import Union, Dict, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal

from app.schemas.ecommerce import (
    Product, Order, ReturnRequest, Promotion, Policy, 
    CustomerFeedback, ProductSearchRequest
)


class EcommerceTools:
    """电商工具类，提供各种电商相关的功能"""
    
    def __init__(self):
        self.logger = logging.getLogger("EcommerceTools")
        # 模拟数据，实际应用中应该连接真实的数据库或API
        self._init_mock_data()
    
    def _init_mock_data(self):
        """初始化模拟数据"""
        # 模拟商品数据
        self.mock_products = [
            {
                "product_id": "P001",
                "name": "无线蓝牙耳机",
                "description": "高品质无线蓝牙耳机，支持降噪功能",
                "price": Decimal("99.99"),
                "original_price": Decimal("149.99"),
                "category": "电子产品",
                "brand": "TechBrand",
                "stock": 50,
                "rating": 4.5,
                "review_count": 128,
                "is_available": True
            },
            {
                "product_id": "P002", 
                "name": "智能手表",
                "description": "多功能智能手表，支持健康监测",
                "price": Decimal("199.99"),
                "category": "电子产品",
                "brand": "SmartTech",
                "stock": 30,
                "rating": 4.3,
                "review_count": 89,
                "is_available": True
            },
            {
                "product_id": "P003",
                "name": "运动鞋",
                "description": "舒适透气运动鞋，适合日常运动",
                "price": Decimal("79.99"),
                "category": "服装鞋帽",
                "brand": "SportBrand",
                "stock": 100,
                "rating": 4.2,
                "review_count": 256,
                "is_available": True
            }
        ]
        
        # 模拟订单数据
        self.mock_orders = [
            {
                "order_id": "ORD001",
                "user_id": "user123",
                "order_number": "20241201001",
                "status": "已发货",
                "total_amount": Decimal("99.99"),
                "created_at": datetime.now() - timedelta(days=2),
                "tracking_number": "TRK123456789",
                "carrier": "顺丰快递"
            },
            {
                "order_id": "ORD002",
                "user_id": "user456", 
                "order_number": "20241201002",
                "status": "已送达",
                "total_amount": Decimal("199.99"),
                "created_at": datetime.now() - timedelta(days=5),
                "tracking_number": "TRK987654321",
                "carrier": "圆通快递"
            }
        ]
        
        # 模拟促销活动
        self.mock_promotions = [
            {
                "promotion_id": "PROMO001",
                "title": "双12大促销",
                "description": "全场商品8折优惠",
                "type": "percentage",
                "discount_value": Decimal("0.2"),
                "start_time": datetime.now() - timedelta(days=1),
                "end_time": datetime.now() + timedelta(days=10),
                "is_active": True
            }
        ]
        
        # 模拟政策规则
        self.mock_policies = [
            {
                "policy_id": "POL001",
                "title": "退换货政策",
                "content": "商品自收货之日起7天内可申请退换货，商品需保持原包装完整。",
                "category": "退换货",
                "version": "1.0",
                "is_active": True
            },
            {
                "policy_id": "POL002",
                "title": "配送政策", 
                "content": "订单满99元免运费，一般3-5个工作日送达。",
                "category": "配送",
                "version": "1.0",
                "is_active": True
            }
        ]


def search_products(query: str, limit: int = 5) -> Union[List[Dict], str]:
    """搜索商品"""
    tools = EcommerceTools()
    
    if not isinstance(query, str) or len(query.strip()) < 2:
        return "错误：搜索关键词必须是至少包含2个字符的字符串。"
    
    try:
        query = query.lower().strip()
        results = []
        
        for product in tools.mock_products:
            # 简单的关键词匹配
            if (query in product["name"].lower() or 
                query in product["description"].lower() or
                query in product["category"].lower() or
                query in product["brand"].lower()):
                results.append({
                    "product_id": product["product_id"],
                    "name": product["name"],
                    "price": float(product["price"]),
                    "category": product["category"],
                    "brand": product["brand"],
                    "rating": product["rating"],
                    "stock": product["stock"],
                    "is_available": product["is_available"]
                })
        
        # 限制返回结果数量
        results = results[:limit]
        
        if not results:
            return f"未找到与'{query}'相关的商品。"
        
        return results
        
    except Exception as e:
        return f"搜索商品时发生错误：{str(e)}"


def get_product_details(product_id: str) -> Union[Dict, str]:
    """获取商品详情"""
    tools = EcommerceTools()
    
    if not isinstance(product_id, str) or not product_id.strip():
        return "错误：商品ID不能为空。"
    
    try:
        for product in tools.mock_products:
            if product["product_id"] == product_id:
                return {
                    "product_id": product["product_id"],
                    "name": product["name"],
                    "description": product["description"],
                    "price": float(product["price"]),
                    "original_price": float(product.get("original_price", product["price"])),
                    "category": product["category"],
                    "brand": product["brand"],
                    "stock": product["stock"],
                    "rating": product["rating"],
                    "review_count": product["review_count"],
                    "is_available": product["is_available"]
                }
        
        return f"未找到商品ID为'{product_id}'的商品。"
        
    except Exception as e:
        return f"获取商品详情时发生错误：{str(e)}"


def get_order_status(order_number: str) -> Union[Dict, str]:
    """查询订单状态"""
    tools = EcommerceTools()
    
    if not isinstance(order_number, str) or not order_number.strip():
        return "错误：订单号不能为空。"
    
    try:
        for order in tools.mock_orders:
            if order["order_number"] == order_number:
                return {
                    "order_number": order["order_number"],
                    "status": order["status"],
                    "total_amount": float(order["total_amount"]),
                    "created_at": order["created_at"].strftime("%Y-%m-%d %H:%M:%S"),
                    "tracking_number": order.get("tracking_number"),
                    "carrier": order.get("carrier")
                }
        
        return f"未找到订单号为'{order_number}'的订单。"
        
    except Exception as e:
        return f"查询订单状态时发生错误：{str(e)}"


def get_active_promotions() -> Union[List[Dict], str]:
    """获取当前活跃的促销活动"""
    tools = EcommerceTools()
    
    try:
        current_time = datetime.now()
        active_promotions = []
        
        for promo in tools.mock_promotions:
            if (promo["is_active"] and 
                promo["start_time"] <= current_time <= promo["end_time"]):
                active_promotions.append({
                    "promotion_id": promo["promotion_id"],
                    "title": promo["title"],
                    "description": promo["description"],
                    "discount_value": float(promo["discount_value"]),
                    "end_time": promo["end_time"].strftime("%Y-%m-%d %H:%M:%S")
                })
        
        if not active_promotions:
            return "当前没有活跃的促销活动。"
        
        return active_promotions
        
    except Exception as e:
        return f"获取促销活动时发生错误：{str(e)}"


def get_policy(category: str) -> Union[Dict, str]:
    """获取政策规则"""
    tools = EcommerceTools()
    
    if not isinstance(category, str) or not category.strip():
        return "错误：政策类别不能为空。"
    
    try:
        category = category.strip()
        
        for policy in tools.mock_policies:
            if policy["is_active"] and category in policy["category"]:
                return {
                    "policy_id": policy["policy_id"],
                    "title": policy["title"],
                    "content": policy["content"],
                    "category": policy["category"],
                    "version": policy["version"]
                }
        
        return f"未找到关于'{category}'的政策规则。"
        
    except Exception as e:
        return f"获取政策规则时发生错误：{str(e)}"


def check_return_eligibility(order_number: str, product_id: str) -> Union[Dict, str]:
    """检查退货资格"""
    if not isinstance(order_number, str) or not isinstance(product_id, str):
        return "错误：订单号和商品ID必须是字符串。"
    
    try:
        # 模拟检查逻辑
        order_info = get_order_status(order_number)
        if isinstance(order_info, str):
            return order_info
        
        # 检查订单时间（7天内可退货）
        order_date = datetime.strptime(order_info["created_at"], "%Y-%m-%d %H:%M:%S")
        days_since_order = (datetime.now() - order_date).days
        
        if days_since_order <= 7:
            return {
                "eligible": True,
                "reason": "商品在退货期限内",
                "days_remaining": 7 - days_since_order
            }
        else:
            return {
                "eligible": False,
                "reason": "已超过7天退货期限",
                "days_since_order": days_since_order
            }
            
    except Exception as e:
        return f"检查退货资格时发生错误：{str(e)}"


def submit_return_request(order_number: str, product_id: str, reason: str) -> Union[Dict, str]:
    """提交退货申请"""
    if not all([isinstance(order_number, str), isinstance(product_id, str), isinstance(reason, str)]):
        return "错误：所有参数必须是字符串。"
    
    if not reason.strip():
        return "错误：退货原因不能为空。"
    
    try:
        # 先检查退货资格
        eligibility = check_return_eligibility(order_number, product_id)
        if isinstance(eligibility, str) or not eligibility.get("eligible", False):
            return "退货申请失败：" + (eligibility if isinstance(eligibility, str) else eligibility.get("reason", "不符合退货条件"))
        
        # 生成退货申请
        return_id = f"RET{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        return {
            "return_id": return_id,
            "order_number": order_number,
            "product_id": product_id,
            "reason": reason,
            "status": "已提交",
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "message": "退货申请已成功提交，我们将在1-2个工作日内处理。"
        }
        
    except Exception as e:
        return f"提交退货申请时发生错误：{str(e)}"


def log_customer_feedback(feedback_type: str, subject: str, content: str, rating: Optional[int] = None) -> Union[Dict, str]:
    """记录客户反馈"""
    if not all([isinstance(feedback_type, str), isinstance(subject, str), isinstance(content, str)]):
        return "错误：反馈类型、主题和内容必须是字符串。"
    
    if not all([feedback_type.strip(), subject.strip(), content.strip()]):
        return "错误：反馈类型、主题和内容不能为空。"
    
    try:
        feedback_id = f"FB{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        return {
            "feedback_id": feedback_id,
            "type": feedback_type,
            "subject": subject,
            "content": content,
            "rating": rating,
            "status": "已收到",
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "message": "感谢您的反馈，我们会认真处理您的意见。"
        }
        
    except Exception as e:
        return f"记录客户反馈时发生错误：{str(e)}"
