# 傲基智能客服平台 (Aukey AI Customer Service Platform)

基于RAG技术的专业电商智能客服系统，采用Microsoft AutoGen框架构建。

## 🚀 项目特色

- **智能对话**: 基于AutoGen 0.5.7框架的多智能体对话系统
- **RAG知识库**: 支持Milvus向量数据库的专业知识检索
- **流式响应**: 实时流式对话体验，类似ChatGPT
- **电商专用**: 内置电商FAQ和客服工具集
- **现代UI**: 参考DeepSeek Chat的简洁现代界面设计

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI + Python 3.10+
- **AI框架**: Microsoft AutoGen 0.5.7
- **向量数据库**: Milvus (生产环境) / ChromaDB (开发环境)
- **LLM**: 支持OpenAI兼容API (DeepSeek等)
- **环境管理**: Conda (aukey_autogen环境)

### 前端技术栈
- **框架**: React 18 + TypeScript
- **UI库**: Tailwind CSS + Headless UI
- **状态管理**: Zustand
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 📁 项目结构

```
aukey_autogen1.0/
├── client/backend/            # 主要后端服务 (基于c_app)
│   ├── c_app/                # FastAPI应用
│   │   ├── api/              # API路由
│   │   ├── core/             # 核心配置
│   │   ├── services/         # 业务服务
│   │   ├── schemas/          # 数据模型
│   │   └── main.py           # 应用入口
│   ├── data/                 # 数据存储
│   │   ├── knowledge_base/   # 知识库文件 (含电商FAQ)
│   │   ├── sessions/         # 会话数据
│   │   └── memories/         # 记忆数据
│   └── requirements.txt      # Python依赖
├── frontend/                  # 前端应用 (React + TypeScript)
│   ├── src/                  # 源代码
│   │   ├── components/       # React组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── stores/          # 状态管理 (Zustand)
│   │   └── types/           # TypeScript类型
│   ├── public/              # 静态资源
│   └── package.json         # 前端依赖
├── backend/                   # 参考后端 (可选)
├── start_dev.py              # 一键启动脚本
├── test_system.py            # 系统测试脚本
└── .env.example             # 环境变量示例
```

## 🚀 快速开始

### 一键启动（推荐）

1. **创建Conda环境**
```bash
conda create -n aukey_autogen python=3.10
conda activate aukey_autogen
```

2. **一键启动开发环境**
```bash
python start_dev.py
```

这个脚本会自动：
- 检查环境依赖
- 安装必要的包
- 创建配置文件
- 启动前后端服务

### 手动启动

如果您喜欢手动控制，可以分别启动：

1. **安装后端依赖**
```bash
cd client/backend
pip install -r requirements.txt
```

2. **安装前端依赖**
```bash
cd frontend
npm install
```

3. **配置环境变量**
编辑 `.env` 文件，填入您的API密钥：
```bash
OPENAI_API_KEY=your_deepseek_api_key_here
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat
```

4. **启动后端服务**
```bash
cd client/backend
python -m uvicorn c_app.main:app --host 0.0.0.0 --port 8000 --reload
```

5. **启动前端服务**
```bash
cd frontend
npm run dev
```

### 系统测试

运行测试脚本验证系统状态：
```bash
python test_system.py
```

### 访问应用
- 🌐 前端界面: http://localhost:3000
- 🔧 后端API: http://localhost:8000
- 📚 API文档: http://localhost:8000/docs

## 📚 功能特性

### 智能对话
- ✅ 流式对话响应
- ✅ 多轮对话记忆
- ✅ 会话管理
- ✅ 上下文理解

### 知识库管理
- ✅ 电商FAQ知识库
- ✅ 文件上传支持
- ✅ 向量化存储
- ✅ 智能检索

### 电商功能
- ✅ 商品咨询
- ✅ 订单查询
- ✅ 退换货处理
- ✅ 政策咨询

### 系统管理
- ✅ 用户会话管理
- ✅ 对话历史记录
- ✅ 系统监控
- ✅ 性能优化

## 🔧 开发指南

### API接口

主要API端点：

- `POST /api/v1/chat/stream` - 流式对话
- `POST /api/v1/chat/session/create` - 创建会话
- `GET /api/v1/chat/session/{session_id}` - 获取会话
- `GET /api/v1/chat/sessions/{user_id}` - 用户会话列表

### 自定义配置

在 `client/backend/c_app/core/config.py` 中修改系统配置：

```python
class Settings(BaseSettings):
    API_V1_STR: str = "/api"
    LLM_MODEL: str = "deepseek-chat"
    KNOWLEDGE_BASE_DIR: str = "data/knowledge_base"
    # ... 其他配置
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

---

**傲基智能客服平台** - 让AI客服更智能，让客户体验更优质！
