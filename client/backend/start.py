#!/usr/bin/env python3
"""
简单的后端启动脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🚀 启动傲基智能客服平台后端...")
print(f"📁 当前目录: {current_dir}")
print(f"🐍 Python路径: {sys.path[0]}")

try:
    # 导入应用
    from c_app.main import app
    import uvicorn
    
    print("✅ 应用导入成功")
    print("🔥 启动服务器...")
    print("📡 API文档: http://localhost:8000/docs")
    print("🌐 健康检查: http://localhost:8000/health")
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("\n💡 请检查:")
    print("1. 是否在 client/backend 目录下运行")
    print("2. 是否安装了所有依赖: pip install -r requirements.txt")
    print("3. 是否激活了正确的conda环境: conda activate aukey_autogen")
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
