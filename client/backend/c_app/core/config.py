import os
import secrets
from typing import List, Optional, Union

from pydantic_settings import BaseSettings


# 定义一个函数返回默认CORS源
def get_default_cors():
    return ["http://localhost:3000", "http://localhost:8000", "http://localhost",
            "http://127.0.0.1:3000", "http://127.0.0.1:8000", "http://127.0.0.1",
            "https://localhost:3000", "https://localhost:8000", "https://localhost",
            "https://127.0.0.1:3000", "https://127.0.0.1:8000", "https://127.0.0.1",
            "ws://localhost:3000", "ws://localhost:8000", "ws://localhost",
            "ws://127.0.0.1:3000", "ws://127.0.0.1:8000", "ws://127.0.0.1",
            "wss://localhost:3000", "wss://localhost:8000", "wss://localhost",
            "wss://127.0.0.1:3000", "wss://127.0.0.1:8000", "wss://127.0.0.1",
            "http://localhost:3000/api", "http://localhost:8000/api", "http://localhost/api",
            "http://127.0.0.1:3000/api", "http://127.0.0.1:8000/api", "http://127.0.0.1/api",
            "http://localhost:3000/api/v1", "http://localhost:8000/api/v1", "http://localhost/api/v1",
            "http://127.0.0.1:3000/api/v1", "http://127.0.0.1:8000/api/v1", "http://127.0.0.1/api/v1",
            "*"]  # 添加通配符以支持所有来源（仅用于开发环境）


class Settings(BaseSettings):
    """傲基智能客服平台配置类"""

    # 基础配置
    API_V1_STR: str = "/api"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    # 访问令牌过期时间（分钟）
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # CORS允许的源
    BACKEND_CORS_ORIGINS: Optional[List[str]] = None

    # LLM配置 (支持DeepSeek等OpenAI兼容API)
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_API_BASE: Optional[str] = "https://api.deepseek.com/v1"
    LLM_MODEL: str = "deepseek-chat"
    LLM_TEMPERATURE: float = 0.7
    LLM_MAX_TOKENS: int = 4000

    # 知识库相关配置
    KNOWLEDGE_BASE_DIR: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "knowledge_base")
    UPLOAD_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    SUPPORTED_FILE_TYPES: List[str] = [".pdf", ".docx", ".txt", ".md"]

    # Milvus向量数据库配置 (生产环境)
    USE_MILVUS: bool = False  # 开发环境默认关闭，生产环境开启
    MILVUS_HOST: str = "localhost"
    MILVUS_PORT: int = 19530
    MILVUS_USER: Optional[str] = None
    MILVUS_PASSWORD: Optional[str] = None
    MILVUS_DATABASE: str = "aukey_knowledge"
    MILVUS_COLLECTION_NAME: str = "knowledge_base"

    # ChromaDB配置 (开发环境备用)
    CHROMA_DB_HOST: str = "localhost"
    CHROMA_DB_PORT: int = 8000
    CHROMA_DB_SSL: bool = False
    CHROMA_COLLECTION_NAME: str = "aukey_knowledge"

    # 会话配置
    SESSION_STORAGE_DIR: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "sessions")
    MAX_SESSION_HISTORY: int = 100
    SESSION_TIMEOUT_HOURS: int = 24

    # 记忆配置
    MEMORY_STORAGE_DIR: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "memories")
    VECTOR_DIMENSION: int = 1536  # OpenAI embedding维度
    SIMILARITY_THRESHOLD: float = 0.7
    MAX_RETRIEVAL_RESULTS: int = 5

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")

    # 电商API配置
    ECOMMERCE_API_BASE: str = "http://localhost:8001"
    ECOMMERCE_API_TIMEOUT: int = 30

    # AutoGen配置
    AUTOGEN_CACHE_SEED: int = 42
    AUTOGEN_MAX_CONSECUTIVE_AUTO_REPLY: int = 10

    # 系统提示词
    SYSTEM_PROMPT: str = """你是傲基智能客服助手，专门为电商平台提供专业的客户服务。

你的职责包括：
1. 回答客户关于商品的咨询
2. 处理订单查询和物流跟踪
3. 协助处理退换货申请
4. 解答平台政策和规则
5. 提供专业的购物建议

请始终保持：
- 友好、专业的服务态度
- 准确、及时的信息回复
- 耐心、细致的问题解答
- 主动、贴心的服务意识

如果遇到无法解决的问题，请及时转接人工客服。"""

    def model_post_init(self, __context):
        """在模型初始化后设置默认值"""
        if self.BACKEND_CORS_ORIGINS is None:
            self.BACKEND_CORS_ORIGINS = get_default_cors()

        # 确保必要的目录存在
        os.makedirs(self.KNOWLEDGE_BASE_DIR, exist_ok=True)
        os.makedirs(self.SESSION_STORAGE_DIR, exist_ok=True)
        os.makedirs(self.MEMORY_STORAGE_DIR, exist_ok=True)
        os.makedirs(self.LOG_DIR, exist_ok=True)

    model_config = {
        "case_sensitive": True,
        "env_file": ".env",
    }


# 创建全局设置实例
settings = Settings()