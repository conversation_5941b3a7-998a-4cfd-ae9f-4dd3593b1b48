from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from c_app.api import api_router
from c_app.core.config import settings

app = FastAPI(
    title="傲基智能客服平台API",
    description="基于RAG技术的专业电商智能客服系统API，使用AutoGen 0.5.7框架",
    version="1.0.0",
)

# 设置CORS，允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {
        "message": "傲基智能客服平台API服务正常运行",
        "version": "1.0.0",
        "status": "healthy",
        "framework": "AutoGen 0.5.7"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "aukey-ai-customer-service",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动傲基智能客服平台...")
    uvicorn.run("c_app.main:app", host="0.0.0.0", port=8000, reload=True)
