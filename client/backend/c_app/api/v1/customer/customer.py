from fastapi import <PERSON>Router, Depends, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
from typing import List, Dict, Any
import json

from ....services.chat_service_v3 import ChatService
from ....services.session_service import SessionService
from ....schemas.customer import ChatRequest, ChatMessage, MemoryAddRequest, MemoryRetrieveRequest, SessionRequest, SessionResponse

router = APIRouter()

# 初始化服务
session_service = SessionService()


@router.post("/stream", response_model=None)
async def chat_stream(
    request: ChatRequest
):
    """流式聊天API"""
    try:
        chat_service = ChatService()

        # 保存用户消息到会话
        if request.session_id:
            # 添加用户消息到会话
            user_message = request.messages[-1]
            session_service.add_message(request.session_id, user_message)

        async def response_generator():
            assistant_response = ""
            async for chunk in chat_service.chat_stream(
                messages=request.messages,
                user_id=request.user_id,
                session_id=request.session_id
            ):
                # 确保每个chunk立即发送
                if chunk:  # 确保chunk不为空
                    assistant_response += chunk
                    encoded_chunk = json.dumps({"content": chunk})
                    yield f"data: {encoded_chunk}\n\n"
            # 保存AI回复到会话
            if request.session_id and assistant_response:
                assistant_message = ChatMessage(role="assistant", content=assistant_response)
                session_service.add_message(request.session_id, assistant_message)

        return StreamingResponse(
            response_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Transfer-Encoding": "chunked",
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/session/create", response_model=SessionResponse)
async def create_session(request: SessionRequest):
    """创建新的聊天会话"""
    try:
        session_data = session_service.create_session(request.user_id)
        return SessionResponse(
            session_id=session_data["session_id"],
            user_id=session_data["user_id"],
            created_at=session_data["created_at"],
            last_active=session_data["last_active"],
            messages=session_data["messages"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/session/{session_id}", response_model=SessionResponse)
async def get_session(session_id: str):
    """获取会话信息和历史消息"""
    try:
        session_data = session_service.get_session(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="会话不存在")

        return SessionResponse(
            session_id=session_data["session_id"],
            user_id=session_data["user_id"],
            created_at=session_data["created_at"],
            last_active=session_data["last_active"],
            messages=session_data["messages"]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions/{user_id}", response_model=List[SessionResponse])
async def get_user_sessions(user_id: str):
    """获取用户的所有会话"""
    try:
        sessions_data = session_service.get_user_sessions(user_id)
        return [
            SessionResponse(
                session_id=session["session_id"],
                user_id=session["user_id"],
                created_at=session["created_at"],
                last_active=session["last_active"],
                messages=session["messages"]
            ) for session in sessions_data
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

