<!DOCTYPE html>
<html>
<head>
    <title>API测试页面</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; border-radius: 4px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; white-space: pre-wrap; border-radius: 4px; max-height: 300px; overflow-y: auto; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .loading { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        h1 { color: #333; text-align: center; }
        h3 { color: #555; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 傲基智能客服API测试</h1>
        <p style="text-align: center; color: #666;">测试后端API是否正常工作</p>
        
        <div class="test-section">
            <h3>1. 健康检查</h3>
            <button onclick="testHealth()">测试健康检查</button>
            <div id="health-result" class="result">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>2. 创建会话</h3>
            <button onclick="testCreateSession()">创建会话</button>
            <div id="session-result" class="result">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>3. 流式聊天</h3>
            <button onclick="testStreamChat()">测试聊天</button>
            <div id="chat-result" class="result">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>4. 获取会话列表</h3>
            <button onclick="testGetSessions()">获取会话列表</button>
            <div id="sessions-result" class="result">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        function setLoading(elementId, message = '正在测试...') {
            const element = document.getElementById(elementId);
            element.className = 'result loading';
            element.textContent = message;
        }

        function setSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'result success';
            element.textContent = message;
        }

        function setError(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'result error';
            element.textContent = message;
        }

        async function testHealth() {
            setLoading('health-result', '正在检查健康状态...');
            try {
                const response = await fetch('/health');
                const data = await response.json();
                setSuccess('health-result', `✅ 健康检查成功!\n\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                setError('health-result', `❌ 健康检查失败: ${error.message}`);
            }
        }

        async function testCreateSession() {
            setLoading('session-result', '正在创建会话...');
            try {
                console.log('🔍 发送请求到: /api/v1/chat/session/create');
                
                const response = await fetch('/api/v1/chat/session/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 'test-user-' + Date.now()
                    })
                });
                
                console.log('📡 响应状态:', response.status);
                console.log('📡 响应头:', [...response.headers.entries()]);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                setSuccess('session-result', `✅ 会话创建成功!\n\n${JSON.stringify(data, null, 2)}`);
                
                // 保存session_id供后续测试使用
                window.testSessionId = data.session_id;
                
            } catch (error) {
                setError('session-result', `❌ 创建会话失败: ${error.message}`);
                console.error('❌ 创建会话详细错误:', error);
            }
        }

        async function testStreamChat() {
            setLoading('chat-result', '正在发送聊天消息...');
            try {
                console.log('🔍 发送请求到: /api/v1/chat/stream');
                
                const requestBody = {
                    messages: [
                        {
                            role: 'user',
                            content: '你好，我想咨询一下商品信息'
                        }
                    ],
                    user_id: 'test-user-' + Date.now()
                };
                
                if (window.testSessionId) {
                    requestBody.session_id = window.testSessionId;
                }
                
                const response = await fetch('/api/v1/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                console.log('📡 响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const resultDiv = document.getElementById('chat-result');
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 流式响应开始:\n\n';
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponse = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                resultDiv.textContent += '\n\n🎉 流式响应完成!';
                                return;
                            }
                            
                            try {
                                const parsed = JSON.parse(data);
                                const content = parsed.content || '';
                                if (content) {
                                    resultDiv.textContent += content;
                                    fullResponse += content;
                                }
                            } catch (e) {
                                console.warn('⚠️ 解析数据失败:', data);
                            }
                        }
                    }
                }
                
                if (fullResponse) {
                    resultDiv.textContent += '\n\n🎉 流式响应完成!';
                } else {
                    resultDiv.textContent += '\n\n⚠️ 没有收到响应内容';
                }
                
            } catch (error) {
                setError('chat-result', `❌ 流式聊天失败: ${error.message}`);
                console.error('❌ 流式聊天详细错误:', error);
            }
        }

        async function testGetSessions() {
            setLoading('sessions-result', '正在获取会话列表...');
            try {
                const response = await fetch('/api/v1/chat/sessions/test-user-123');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                setSuccess('sessions-result', `✅ 获取会话列表成功!\n\n${JSON.stringify(data, null, 2)}`);
                
            } catch (error) {
                setError('sessions-result', `❌ 获取会话列表失败: ${error.message}`);
                console.error('❌ 获取会话列表详细错误:', error);
            }
        }

        // 页面加载完成后自动运行健康检查
        window.onload = function() {
            console.log('🚀 页面加载完成，开始自动健康检查...');
            testHealth();
        };
    </script>
</body>
</html>
