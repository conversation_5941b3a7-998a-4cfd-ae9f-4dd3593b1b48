#!/usr/bin/env python3
"""
傲基智能客服平台 - 直接启动脚本
"""

import sys
import os
from pathlib import Path

# 确保当前目录在Python路径中
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🚀 启动傲基智能客服平台...")
print(f"📁 当前目录: {current_dir}")

try:
    # 直接导入应用
    from c_app.main import app
    import uvicorn
    
    print("✅ 应用导入成功")
    print("🔥 启动服务器...")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("\n💡 可能的解决方案:")
    print("1. 检查是否在正确的目录: cd client/backend")
    print("2. 检查Python路径是否正确")
    print("3. 确保所有依赖已安装: pip install -r requirements.txt")
    
    # 尝试显示当前目录结构
    print(f"\n📁 当前目录内容:")
    try:
        for item in current_dir.iterdir():
            print(f"   {item.name}")
    except:
        pass
        
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
