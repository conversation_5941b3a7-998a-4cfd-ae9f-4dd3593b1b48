<!DOCTYPE html>
<html>
<head>
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; white-space: pre-wrap; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .success { background: #d4edda; border-color: #c3e6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>傲基智能客服API测试</h1>
        
        <div class="test-section">
            <h3>1. 健康检查</h3>
            <button onclick="testHealth()">测试健康检查</button>
            <div id="health-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 创建会话</h3>
            <button onclick="testCreateSession()">创建会话</button>
            <div id="session-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 流式聊天</h3>
            <button onclick="testStreamChat()">测试聊天</button>
            <div id="chat-result" class="result"></div>
        </div>
    </div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch('/health');
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.textContent = `成功: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
            }
        }

        async function testCreateSession() {
            const resultDiv = document.getElementById('session-result');
            try {
                console.log('发送请求到: /api/v1/chat/session/create');
                const response = await fetch('/api/v1/chat/session/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 'test-user'
                    })
                });
                
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.textContent = `成功: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
                console.error('创建会话失败:', error);
            }
        }

        async function testStreamChat() {
            const resultDiv = document.getElementById('chat-result');
            try {
                console.log('发送请求到: /api/v1/chat/stream');
                const response = await fetch('/api/v1/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [
                            {
                                role: 'user',
                                content: '你好，我想咨询商品信息'
                            }
                        ],
                        user_id: 'test-user'
                    })
                });
                
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '流式响应:\n';
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                resultDiv.textContent += '\n\n完成';
                                return;
                            }
                            
                            try {
                                const parsed = JSON.parse(data);
                                resultDiv.textContent += parsed.content || '';
                            } catch (e) {
                                console.warn('解析失败:', data);
                            }
                        }
                    }
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `错误: ${error.message}`;
                console.error('流式聊天失败:', error);
            }
        }
    </script>
</body>
</html>
