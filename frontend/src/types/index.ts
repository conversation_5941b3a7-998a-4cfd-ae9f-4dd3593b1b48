// 聊天相关类型定义

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  isStreaming?: boolean
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: number
  updatedAt: number
  userId: string
}

export interface ChatRequest {
  messages: Omit<ChatMessage, 'id' | 'timestamp'>[]
  model?: string
  system_prompt?: string
  user_id?: string
  session_id?: string
}

export interface ChatResponse {
  content: string
  role: 'assistant'
  session_id?: string
}

// 用户相关类型
export interface User {
  id: string
  name: string
  avatar?: string
  email?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 会话管理类型
export interface SessionCreateRequest {
  user_id: string
  title?: string
}

export interface SessionResponse {
  session_id: string
  title: string
  created_at: string
  updated_at: string
}

// 知识库相关类型
export interface KnowledgeBase {
  id: string
  name: string
  description: string
  fileCount: number
  createdAt: number
  updatedAt: number
}

export interface UploadFile {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

// 系统配置类型
export interface SystemConfig {
  apiBaseUrl: string
  maxMessageLength: number
  supportedFileTypes: string[]
  maxFileSize: number
}

// 流式响应类型
export interface StreamChunk {
  content: string
  done: boolean
  session_id?: string
}

// 错误类型
export interface ApiError {
  code: string
  message: string
  details?: any
}

// 组件Props类型
export interface ChatInputProps {
  onSendMessage: (message: string) => void
  disabled?: boolean
  placeholder?: string
}

export interface MessageListProps {
  messages: ChatMessage[]
  isLoading?: boolean
}

export interface SidebarProps {
  sessions: ChatSession[]
  currentSessionId?: string
  onSessionSelect: (sessionId: string) => void
  onNewSession: () => void
  onDeleteSession: (sessionId: string) => void
}

// 状态管理类型
export interface ChatStore {
  // 状态
  sessions: ChatSession[]
  currentSession: ChatSession | null
  isLoading: boolean
  error: string | null
  
  // 操作
  createSession: (title?: string) => Promise<string>
  loadSession: (sessionId: string) => Promise<void>
  sendMessage: (content: string) => Promise<void>
  deleteSession: (sessionId: string) => Promise<void>
  clearError: () => void
}

export interface UIStore {
  // 状态
  sidebarOpen: boolean
  theme: 'light' | 'dark'
  
  // 操作
  toggleSidebar: () => void
  setTheme: (theme: 'light' | 'dark') => void
}
