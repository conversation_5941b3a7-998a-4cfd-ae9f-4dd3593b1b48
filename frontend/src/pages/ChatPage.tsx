import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { useChatStore } from '@/stores/chatStore'
import { useUIStore } from '@/stores/uiStore'
import Sidebar from '@/components/Sidebar'
import MessageList from '@/components/MessageList'
import ChatInput from '@/components/ChatInput'
import toast from 'react-hot-toast'

const ChatPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>()
  const navigate = useNavigate()
  
  const {
    sessions,
    currentSession,
    isLoading,
    error,
    createSession,
    loadSession,
    sendMessage,
    deleteSession,
    clearError,
  } = useChatStore()
  
  const { sidebarOpen, toggleSidebar } = useUIStore()
  const [isMobile, setIsMobile] = useState(false)

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 处理会话ID变化
  useEffect(() => {
    if (sessionId && sessionId !== currentSession?.id) {
      loadSession(sessionId)
    }
  }, [sessionId, currentSession?.id, loadSession])

  // 处理错误
  useEffect(() => {
    if (error) {
      toast.error(error)
      clearError()
    }
  }, [error, clearError])

  // 创建新会话
  const handleNewSession = async () => {
    try {
      const newSessionId = await createSession()
      navigate(`/chat/${newSessionId}`)
      
      // 移动端自动关闭侧边栏
      if (isMobile && sidebarOpen) {
        toggleSidebar()
      }
    } catch (error) {
      // 错误已在store中处理
    }
  }

  // 选择会话
  const handleSessionSelect = (sessionId: string) => {
    navigate(`/chat/${sessionId}`)
    
    // 移动端自动关闭侧边栏
    if (isMobile && sidebarOpen) {
      toggleSidebar()
    }
  }

  // 删除会话
  const handleDeleteSession = async (sessionId: string) => {
    try {
      await deleteSession(sessionId)
      
      // 如果删除的是当前会话，导航到首页
      if (sessionId === currentSession?.id) {
        navigate('/')
      }
    } catch (error) {
      // 错误已在store中处理
    }
  }

  // 发送消息
  const handleSendMessage = async (content: string) => {
    try {
      await sendMessage(content)
    } catch (error) {
      // 错误已在store中处理
    }
  }

  return (
    <div className="flex h-screen bg-white">
      {/* 移动端遮罩 */}
      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50"
          onClick={toggleSidebar}
        />
      )}

      {/* 侧边栏 */}
      <div
        className={`${
          isMobile
            ? `fixed left-0 top-0 z-50 h-full transform transition-transform duration-300 ${
                sidebarOpen ? 'translate-x-0' : '-translate-x-full'
              }`
            : sidebarOpen
            ? 'flex-shrink-0'
            : 'hidden'
        }`}
      >
        <Sidebar
          sessions={sessions}
          currentSessionId={currentSession?.id}
          onSessionSelect={handleSessionSelect}
          onNewSession={handleNewSession}
          onDeleteSession={handleDeleteSession}
        />
      </div>

      {/* 主聊天区域 */}
      <div className="flex flex-1 flex-col min-w-0">
        {/* 顶部工具栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center space-x-3">
            {/* 侧边栏切换按钮 */}
            <button
              onClick={toggleSidebar}
              className="flex items-center justify-center w-8 h-8 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {sidebarOpen ? (
                <XMarkIcon className="w-5 h-5 text-gray-600" />
              ) : (
                <Bars3Icon className="w-5 h-5 text-gray-600" />
              )}
            </button>

            {/* 当前会话标题 */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {currentSession?.title || '傲基智能客服'}
              </h2>
              {currentSession && (
                <p className="text-sm text-gray-500">
                  {currentSession.messages.length} 条消息
                </p>
              )}
            </div>
          </div>

          {/* 右侧操作按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleNewSession}
              className="btn-primary text-sm"
              disabled={isLoading}
            >
              新对话
            </button>
          </div>
        </div>

        {/* 消息区域 */}
        <div className="flex-1 flex flex-col min-h-0">
          <MessageList
            messages={currentSession?.messages || []}
            isLoading={isLoading}
          />
          
          {/* 输入区域 */}
          <ChatInput
            onSendMessage={handleSendMessage}
            disabled={isLoading}
            placeholder={
              currentSession
                ? "继续对话..."
                : "开始新对话，输入您的问题..."
            }
          />
        </div>
      </div>
    </div>
  )
}

export default ChatPage
