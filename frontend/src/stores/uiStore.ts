import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { UIStore } from '@/types'

export const useUIStore = create<UIStore>()(
  persist(
    (set) => ({
      // 初始状态
      sidebarOpen: true,
      theme: 'light',

      // 操作
      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }))
      },

      setTheme: (theme: 'light' | 'dark') => {
        set({ theme })
        
        // 更新HTML类名以支持主题切换
        if (theme === 'dark') {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      },
    }),
    {
      name: 'ui-store', // 本地存储的key
      partialize: (state) => ({ 
        theme: state.theme,
        sidebarOpen: state.sidebarOpen 
      }),
    }
  )
)
