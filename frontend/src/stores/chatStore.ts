import { create } from 'zustand'
import { v4 as uuidv4 } from 'uuid'
import { ChatStore, ChatSession, ChatMessage } from '@/types'
import { apiService } from '@/services/api'
import toast from 'react-hot-toast'

const DEFAULT_USER_ID = 'default-user'

export const useChatStore = create<ChatStore>((set, get) => ({
  // 初始状态
  sessions: [],
  currentSession: null,
  isLoading: false,
  error: null,

  // 创建新会话
  createSession: async (title?: string) => {
    try {
      set({ isLoading: true, error: null })
      
      const response = await apiService.createSession({
        user_id: DEFAULT_USER_ID,
        title: title || `新对话 ${new Date().toLocaleString()}`,
      })

      const newSession: ChatSession = {
        id: response.session_id,
        title: response.title,
        messages: [],
        createdAt: new Date(response.created_at).getTime(),
        updatedAt: new Date(response.updated_at).getTime(),
        userId: DEFAULT_USER_ID,
      }

      set((state) => ({
        sessions: [newSession, ...state.sessions],
        currentSession: newSession,
        isLoading: false,
      }))

      return newSession.id
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建会话失败'
      set({ error: errorMessage, isLoading: false })
      toast.error(errorMessage)
      throw error
    }
  },

  // 加载会话
  loadSession: async (sessionId: string) => {
    try {
      set({ isLoading: true, error: null })
      
      // 先检查本地是否已有该会话
      const existingSession = get().sessions.find(s => s.id === sessionId)
      if (existingSession) {
        set({ currentSession: existingSession, isLoading: false })
        return
      }

      // 从服务器获取会话信息
      const response = await apiService.getSession(sessionId)
      
      const session: ChatSession = {
        id: response.session_id,
        title: response.title,
        messages: [], // 消息需要单独加载
        createdAt: new Date(response.created_at).getTime(),
        updatedAt: new Date(response.updated_at).getTime(),
        userId: DEFAULT_USER_ID,
      }

      set((state) => ({
        sessions: state.sessions.some(s => s.id === sessionId) 
          ? state.sessions 
          : [session, ...state.sessions],
        currentSession: session,
        isLoading: false,
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载会话失败'
      set({ error: errorMessage, isLoading: false })
      toast.error(errorMessage)
    }
  },

  // 发送消息
  sendMessage: async (content: string) => {
    const { currentSession } = get()
    
    if (!currentSession) {
      // 如果没有当前会话，创建一个新的
      const sessionId = await get().createSession()
      await get().loadSession(sessionId)
    }

    const session = get().currentSession!
    
    // 创建用户消息
    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content,
      timestamp: Date.now(),
    }

    // 创建助手消息占位符
    const assistantMessage: ChatMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: '',
      timestamp: Date.now(),
      isStreaming: true,
    }

    // 更新会话消息
    set((state) => ({
      currentSession: {
        ...session,
        messages: [...session.messages, userMessage, assistantMessage],
        updatedAt: Date.now(),
      },
      isLoading: true,
    }))

    try {
      // 准备聊天请求
      const chatRequest = {
        messages: [
          ...session.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          { role: 'user' as const, content },
        ],
        user_id: DEFAULT_USER_ID,
        session_id: session.id,
      }

      // 开始流式响应
      const stream = await apiService.streamChat(chatRequest)
      const reader = stream.getReader()

      let fullContent = ''
      
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break
        
        if (value.content) {
          fullContent += value.content
          
          // 更新助手消息内容
          set((state) => {
            if (!state.currentSession) return state
            
            const updatedMessages = state.currentSession.messages.map(msg =>
              msg.id === assistantMessage.id
                ? { ...msg, content: fullContent }
                : msg
            )
            
            return {
              currentSession: {
                ...state.currentSession,
                messages: updatedMessages,
              },
            }
          })
        }
      }

      // 完成流式响应
      set((state) => {
        if (!state.currentSession) return state
        
        const updatedMessages = state.currentSession.messages.map(msg =>
          msg.id === assistantMessage.id
            ? { ...msg, isStreaming: false }
            : msg
        )
        
        const updatedSession = {
          ...state.currentSession,
          messages: updatedMessages,
          updatedAt: Date.now(),
        }
        
        return {
          currentSession: updatedSession,
          sessions: state.sessions.map(s => 
            s.id === updatedSession.id ? updatedSession : s
          ),
          isLoading: false,
        }
      })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '发送消息失败'
      set({ error: errorMessage, isLoading: false })
      toast.error(errorMessage)
      
      // 移除失败的消息
      set((state) => {
        if (!state.currentSession) return state
        
        return {
          currentSession: {
            ...state.currentSession,
            messages: state.currentSession.messages.filter(
              msg => msg.id !== assistantMessage.id
            ),
          },
        }
      })
    }
  },

  // 删除会话
  deleteSession: async (sessionId: string) => {
    try {
      await apiService.deleteSession(sessionId)
      
      set((state) => ({
        sessions: state.sessions.filter(s => s.id !== sessionId),
        currentSession: state.currentSession?.id === sessionId ? null : state.currentSession,
      }))
      
      toast.success('会话已删除')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '删除会话失败'
      set({ error: errorMessage })
      toast.error(errorMessage)
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null })
  },
}))
