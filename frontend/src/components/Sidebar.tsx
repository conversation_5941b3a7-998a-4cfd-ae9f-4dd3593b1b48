import React, { useState } from 'react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  PlusIcon,
  ChatBubbleLeftIcon,
  TrashIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline'
import { Menu, Transition } from '@headlessui/react'
import { SidebarProps } from '@/types'
import { clsx } from 'clsx'

const Sidebar: React.FC<SidebarProps> = ({
  sessions,
  currentSessionId,
  onSessionSelect,
  onNewSession,
  onDeleteSession,
}) => {
  const [hoveredSessionId, setHoveredSessionId] = useState<string | null>(null)

  const formatSessionTime = (timestamp: number) => {
    const now = new Date()
    const sessionDate = new Date(timestamp)
    const diffInHours = (now.getTime() - sessionDate.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return format(sessionDate, 'HH:mm', { locale: zhCN })
    } else if (diffInHours < 24 * 7) {
      return format(sessionDate, 'MM-dd', { locale: zhCN })
    } else {
      return format(sessionDate, 'yyyy-MM-dd', { locale: zhCN })
    }
  }

  const truncateTitle = (title: string, maxLength: number = 20) => {
    return title.length > maxLength ? `${title.slice(0, maxLength)}...` : title
  }

  return (
    <div className="flex h-full w-80 flex-col bg-gray-50 border-r border-gray-200">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h1 className="text-lg font-semibold text-gray-900">傲基智能客服</h1>
        <button
          onClick={onNewSession}
          className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors"
          title="新建对话"
        >
          <PlusIcon className="w-4 h-4" />
        </button>
      </div>

      {/* 会话列表 */}
      <div className="flex-1 overflow-y-auto">
        {sessions.length === 0 ? (
          // 空状态
          <div className="flex flex-col items-center justify-center h-full p-6 text-gray-500">
            <ChatBubbleLeftIcon className="w-12 h-12 mb-4 text-gray-300" />
            <p className="text-sm text-center">
              还没有对话记录
              <br />
              点击上方按钮开始新对话
            </p>
          </div>
        ) : (
          // 会话列表
          <div className="p-2 space-y-1">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={clsx(
                  'group relative flex items-center p-3 rounded-lg cursor-pointer transition-colors',
                  currentSessionId === session.id
                    ? 'bg-primary-100 border border-primary-200'
                    : 'hover:bg-gray-100'
                )}
                onClick={() => onSessionSelect(session.id)}
                onMouseEnter={() => setHoveredSessionId(session.id)}
                onMouseLeave={() => setHoveredSessionId(null)}
              >
                {/* 会话图标 */}
                <div className="flex-shrink-0 mr-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <ChatBubbleLeftIcon className="w-4 h-4 text-gray-600" />
                  </div>
                </div>

                {/* 会话信息 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {truncateTitle(session.title)}
                    </h3>
                    <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                      {formatSessionTime(session.updatedAt)}
                    </span>
                  </div>
                  
                  {/* 最后一条消息预览 */}
                  {session.messages.length > 0 && (
                    <p className="text-xs text-gray-500 mt-1 truncate">
                      {session.messages[session.messages.length - 1].content}
                    </p>
                  )}
                </div>

                {/* 操作菜单 */}
                {(hoveredSessionId === session.id || currentSessionId === session.id) && (
                  <div className="flex-shrink-0 ml-2">
                    <Menu as="div" className="relative">
                      <Menu.Button className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors">
                        <EllipsisVerticalIcon className="w-4 h-4 text-gray-500" />
                      </Menu.Button>
                      
                      <Transition
                        as={React.Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-1 w-32 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                          <div className="py-1">
                            <Menu.Item>
                              {({ active }) => (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onDeleteSession(session.id)
                                  }}
                                  className={clsx(
                                    'flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50',
                                    active && 'bg-red-50'
                                  )}
                                >
                                  <TrashIcon className="w-4 h-4 mr-2" />
                                  删除对话
                                </button>
                              )}
                            </Menu.Item>
                          </div>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部信息 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          <p>基于 AutoGen 0.5.7 构建</p>
          <p className="mt-1">智能客服 · 专业服务</p>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
