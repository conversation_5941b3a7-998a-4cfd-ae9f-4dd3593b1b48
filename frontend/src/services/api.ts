import axios, { AxiosInstance, AxiosResponse } from 'axios'
import {
  ChatRequest,
  ChatResponse,
  SessionCreateRequest,
  SessionResponse,
  ApiResponse,
  StreamChunk
} from '@/types'

class ApiService {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 可以在这里添加认证token等
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error) => {
        console.error('API Error:', error)
        return Promise.reject(error)
      }
    )
  }

  // 流式聊天
  async streamChat(request: ChatRequest): Promise<ReadableStream<StreamChunk>> {
    const response = await fetch('/api/v1/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    if (!response.body) {
      throw new Error('No response body')
    }

    return new ReadableStream({
      start(controller) {
        const reader = response.body!.getReader()
        const decoder = new TextDecoder()

        function pump(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close()
              return
            }

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6)
                if (data === '[DONE]') {
                  controller.close()
                  return
                }

                try {
                  const parsed = JSON.parse(data)
                  controller.enqueue(parsed)
                } catch (e) {
                  console.warn('Failed to parse SSE data:', data)
                }
              }
            }

            return pump()
          })
        }

        return pump()
      },
    })
  }

  // 创建会话
  async createSession(request: SessionCreateRequest): Promise<SessionResponse> {
    const response = await this.client.post<ApiResponse<SessionResponse>>(
      '/v1/chat/session/create',
      request
    )

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to create session')
    }

    return response.data.data!
  }

  // 获取会话详情
  async getSession(sessionId: string): Promise<SessionResponse> {
    const response = await this.client.get<ApiResponse<SessionResponse>>(
      `/v1/chat/session/${sessionId}`
    )

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get session')
    }

    return response.data.data!
  }

  // 获取用户会话列表
  async getUserSessions(userId: string): Promise<SessionResponse[]> {
    const response = await this.client.get<ApiResponse<SessionResponse[]>>(
      `/v1/chat/sessions/${userId}`
    )

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to get sessions')
    }

    return response.data.data || []
  }

  // 删除会话
  async deleteSession(sessionId: string): Promise<void> {
    const response = await this.client.delete<ApiResponse>(
      `/v1/chat/session/${sessionId}`
    )

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to delete session')
    }
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health')
      return response.status === 200
    } catch {
      return false
    }
  }

  // 上传文件到知识库
  async uploadFile(file: File, onProgress?: (progress: number) => void): Promise<void> {
    const formData = new FormData()
    formData.append('file', file)

    await this.client.post('/knowledge/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
  }
}

export const apiService = new ApiService()
export default apiService
