@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-white text-gray-900;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* 选中文本样式 */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  /* 输入框样式 */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  /* 聊天消息样式 */
  .message-user {
    @apply bg-primary-600 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs ml-auto;
  }
  
  .message-assistant {
    @apply bg-gray-100 text-gray-900 rounded-2xl rounded-bl-md px-4 py-2 max-w-2xl;
  }
  
  /* 代码块样式 */
  .prose pre {
    @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
  }
  
  .prose code {
    @apply bg-gray-100 text-gray-900 px-1 py-0.5 rounded text-sm;
  }
  
  .prose pre code {
    @apply bg-transparent text-gray-100 p-0;
  }
}

@layer utilities {
  /* 动画工具类 */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-typing {
    animation: typing 1s ease-in-out infinite;
  }
  
  /* 布局工具类 */
  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
