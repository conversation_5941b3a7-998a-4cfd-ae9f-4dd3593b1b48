.App {
  min-height: 100vh;
  background: #fafafa;
}

/* 聊天界面特定样式 */
.chat-container {
  display: flex;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.chat-sidebar {
  width: 280px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.chat-input-area {
  border-top: 1px solid #e2e8f0;
  padding: 1rem;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 50;
    transition: left 0.3s ease;
  }
  
  .chat-sidebar.open {
    left: 0;
  }
  
  .chat-main {
    width: 100%;
  }
}

/* 消息动画 */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 打字效果 */
.typing-indicator {
  display: inline-block;
}

.typing-indicator::after {
  content: '|';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
